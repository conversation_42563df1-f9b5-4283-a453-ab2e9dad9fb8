import React, { useMemo } from 'react';
import { TrendingDown } from 'lucide-react';

// Helper: parse date string to Date object (YYYY-MM-DD)
function parseDate(dateStr) {
  if (!dateStr) return null;
  const [year, month, day] = dateStr.split('-').map(Number);
  return new Date(year, month - 1, day);
}

function filterExpensesByPeriod(expenses, period, customDateRange, useCustomRange) {
  if (!Array.isArray(expenses)) return [];
  const now = new Date();
  let start, end;

  if (useCustomRange && customDateRange?.start && customDateRange?.end) {
    start = parseDate(customDateRange.start);
    end = parseDate(customDateRange.end);
    end.setHours(23, 59, 59, 999);
  } else {
    switch (period) {
      case 'day':
        start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        end = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
        break;
      case 'week':
        start = new Date(now);
        start.setDate(now.getDate() - now.getDay()); // Sunday as start
        start.setHours(0, 0, 0, 0);
        end = new Date(start);
        end.setDate(start.getDate() + 7);
        break;
      case 'month':
        start = new Date(now.getFullYear(), now.getMonth(), 1);
        end = new Date(now.getFullYear(), now.getMonth() + 1, 1);
        break;
      case 'year':
        start = new Date(now.getFullYear(), 0, 1);
        end = new Date(now.getFullYear() + 1, 0, 1);
        break;
      default:
        start = null;
        end = null;
    }
  }

  return expenses.filter(exp => {
    if (!exp.date) return false;
    const d = parseDate(exp.date);
    if (start && d < start) return false;
    if (end && d >= end) return false;
    return true;
  });
}

function formatCurrency(amount) {
  return amount?.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' }) || 'Rp0';
}

const ExpenseSummaryCard = ({ selectedPeriod, customDateRange, useCustomRange, periodOptions }) => {
  const expenses = useMemo(() => {
    try {
      const stored = localStorage.getItem('expenses');
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }, []);

  const filteredExpenses = useMemo(
    () => filterExpensesByPeriod(expenses, selectedPeriod, customDateRange, useCustomRange),
    [expenses, selectedPeriod, customDateRange, useCustomRange]
  );
  const totalExpense = filteredExpenses.reduce((sum, exp) => sum + (exp.amount || 0), 0);

  return (
    <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-red-50 to-red-100 border border-red-200 hover:border-red-300 transition-all duration-300 hover:shadow-lg">
      <div className="p-6">
        {/* Header with icon and label */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-gradient-to-br from-red-100 to-red-200">
              <TrendingDown className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <h3 className="text-sm font-semibold text-red-700 uppercase tracking-wide">Total Pengeluaran</h3>
              <p className="text-xs text-red-500 mt-0.5">
                {useCustomRange && customDateRange?.start && customDateRange?.end
                  ? `${customDateRange.start} - ${customDateRange.end}`
                  : periodOptions.find(p => p.value === selectedPeriod)?.label}
              </p>
            </div>
          </div>
        </div>

        {/* Expense amount */}
        <div className="space-y-1">
          <p className="text-2xl font-bold text-red-800 leading-tight">
            {formatCurrency(totalExpense)}
          </p>
          <div className="flex items-center space-x-2">
            <div className="h-1 w-8 bg-gradient-to-r from-red-400 to-red-500 rounded-full"></div>
            <span className="text-xs text-red-600">Total pengeluaran periode ini</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExpenseSummaryCard;
