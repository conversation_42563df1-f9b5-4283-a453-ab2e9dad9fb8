-- Migration: Add items column to invoices table and fix column name
-- This script adds the missing 'items' column to the invoices table and fixes paidat to paidAt

-- Add items column to invoices table
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS items jsonb;

-- Fix column name from paidat to paidAt (if it exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'invoices' AND column_name = 'paidat') THEN
        ALTER TABLE invoices RENAME COLUMN paidat TO paidAt;
    END IF;
END $$;

-- Optional: Add a comment to describe the column
COMMENT ON COLUMN invoices.items IS 'JSON array containing invoice items with produkId, quantity, harga, and tanggal';

-- Optional: Create an index on items column for better query performance
CREATE INDEX IF NOT EXISTS idx_invoices_items ON invoices USING gin (items);
