import { supabase } from './supabaseClient'
import { v4 as uuidv4 } from 'uuid'

// Storage service untuk mengelola data di localStorage

const STORAGE_KEYS = {
  PRODUCTS: 'roti_ragil_products',
  CUSTOMERS: 'roti_ragil_customers',
  INVOICES: 'roti_ragil_invoices',
  SETTINGS: 'roti_ragil_settings',
  DATA_VERSION: 'roti_ragil_data_version'
}

// Current version of default data
const CURRENT_DATA_VERSION = '2.2.0'

// Generic storage functions
const getFromStorage = (key) => {
  try {
    const data = localStorage.getItem(key)
    return data ? JSON.parse(data) : []
  } catch (error) {
    console.error('Error reading from storage:', error)
    return []
  }
}

const saveToStorage = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify(data))
    return true
  } catch (error) {
    console.error('Error saving to storage:', error)
    return false
  }
}

// Data version functions
const getDataVersion = () => {
  try {
    return localStorage.getItem(STORAGE_KEYS.DATA_VERSION) || '1.0.0'
  } catch (error) {
    console.error('Error reading data version:', error)
    return '1.0.0'
  }
}

const setDataVersion = (version) => {
  try {
    localStorage.setItem(STORAGE_KEYS.DATA_VERSION, version)
    return true
  } catch (error) {
    console.error('Error saving data version:', error)
    return false
  }
}

// Supabase CRUD for Products
export const getProducts = async () => {
  const { data, error } = await supabase.from('products').select('*')
  if (error) {
    console.error('Supabase getProducts error:', error)
    return []
  }
  return data
}

export const saveProduct = async (product) => {
  const newProduct = {
    id: uuidv4(),
    ...product,
    createdat: new Date().toISOString(),
  }
  const { data, error } = await supabase.from('products').insert([newProduct]).select()
  if (error) {
    console.error('Supabase saveProduct error:', error)
    return null
  }
  return data[0]
}

export const updateProduct = async (id, updatedProduct) => {
  const { data, error } = await supabase
    .from('products')
    .update(updatedProduct)
    .eq('id', id)
    .select()
  if (error) {
    console.error('Supabase updateProduct error:', error)
    return null
  }
  return data[0]
}

export const deleteProduct = async (id) => {
  const { error } = await supabase.from('products').delete().eq('id', id)
  if (error) {
    console.error('Supabase deleteProduct error:', error)
    return false
  }
  return true
}

// Supabase CRUD for Customers
export const getCustomers = async () => {
  const { data, error } = await supabase.from('customers').select('*')
  if (error) {
    console.error('Supabase getCustomers error:', error)
    return []
  }
  return data
}

export const saveCustomer = async (customer) => {
  const newCustomer = {
    id: uuidv4(),
    ...customer,
    createdat: new Date().toISOString(),
  }
  const { data, error } = await supabase.from('customers').insert([newCustomer]).select()
  if (error) {
    console.error('Supabase saveCustomer error:', error)
    return null
  }
  return data[0]
}

export const updateCustomer = async (id, updatedCustomer) => {
  const { data, error } = await supabase
    .from('customers')
    .update(updatedCustomer)
    .eq('id', id)
    .select()
  if (error) {
    console.error('Supabase updateCustomer error:', error)
    return null
  }
  return data[0]
}

export const deleteCustomer = async (id) => {
  const { error } = await supabase.from('customers').delete().eq('id', id)
  if (error) {
    console.error('Supabase deleteCustomer error:', error)
    return false
  }
  return true
}

// Supabase CRUD for Invoices
export const getInvoices = async () => {
  const { data, error } = await supabase.from('invoices').select('*')
  if (error) {
    console.error('Supabase getInvoices error:', error)
    return []
  }
  return data
}

export const saveInvoice = async (invoice) => {
  // Generate invoice number if not provided
  const nomorinvoice = invoice.nomorinvoice || await generateInvoiceNumber()

  const newInvoice = {
    id: uuidv4(),
    ...invoice,
    nomorinvoice,
    createdat: new Date().toISOString(),
  }
  const { data, error } = await supabase.from('invoices').insert([newInvoice]).select()
  if (error) {
    console.error('Supabase saveInvoice error:', error)
    return null
  }
  return data[0]
}

export const updateInvoice = async (id, updatedInvoice) => {
  const { data, error } = await supabase
    .from('invoices')
    .update(updatedInvoice)
    .eq('id', id)
    .select()
  if (error) {
    console.error('Supabase updateInvoice error:', error)
    return null
  }
  return data[0]
}

export const deleteInvoice = async (id) => {
  const { error } = await supabase.from('invoices').delete().eq('id', id)
  if (error) {
    console.error('Supabase deleteInvoice error:', error)
    return false
  }
  return true
}

export const getInvoiceById = async (id) => {
  const { data, error } = await supabase.from('invoices').select('*').eq('id', id).single()
  if (error) {
    console.error('Supabase getInvoiceById error:', error)
    return null
  }
  return data
}

// Supabase CRUD for Expenses
export const getExpenses = async () => {
  const { data, error } = await supabase.from('expenses').select('*').order('createdat', { ascending: false })
  if (error) {
    console.error('Supabase getExpenses error:', error)
    return []
  }
  return data
}

export const saveExpense = async (expense) => {
  const newExpense = {
    id: uuidv4(),
    ...expense,
    createdat: new Date().toISOString(),
  }
  const { data, error } = await supabase.from('expenses').insert([newExpense]).select()
  if (error) {
    console.error('Supabase saveExpense error:', error)
    return null
  }
  return data[0]
}

export const updateExpense = async (id, updatedExpense) => {
  const { data, error } = await supabase
    .from('expenses')
    .update(updatedExpense)
    .eq('id', id)
    .select()
  if (error) {
    console.error('Supabase updateExpense error:', error)
    return null
  }
  return data[0]
}

export const deleteExpense = async (id) => {
  const { error } = await supabase.from('expenses').delete().eq('id', id)
  if (error) {
    console.error('Supabase deleteExpense error:', error)
    return false
  }
  return true
}

// Helper functions
const generateInvoiceNumber = async () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const invoices = await getInvoices()
  const invoiceCount = invoices.length + 1
  return `INV/${year}${month}/${String(invoiceCount).padStart(4, '0')}`
}

// Default data with consistent IDs for versioning
const getDefaultProducts = () => [
  { nama: 'Roti Sosis', harga: 3000, isdefault: true },
  { nama: 'Roti Sosis', harga: 2500, isdefault: true },
  { nama: 'Roti Meses', harga: 2500, isdefault: true },
  { nama: 'Roti Abon', harga: 2500, isdefault: true },
  { nama: 'Roti Keju', harga: 2500, isdefault: true },
  { nama: 'Roti Kacang Hijau', harga: 2500, isdefault: true },
  { nama: 'Roti Kacang Merah', harga: 2500, isdefault: true },
  { nama: 'Flossroll', harga: 3500, isdefault: true },
  { nama: 'Bolu Gulung', harga: 35000, isdefault: true },
  { nama: 'Bolu Gulung', harga: 30000, isdefault: true },
  { nama: 'Chiffon Keju', harga: 35000, isdefault: true },
  { nama: 'Chiffon Keju', harga: 30000, isdefault: true },
  { nama: 'Flossroll Box', harga: 30000, isdefault: true },
  { nama: 'Roti Pisang Cokelat', harga: 3000, isdefault: true },
  { nama: 'Roti Pisang Keju', harga: 3000, isdefault: true }
]

const getDefaultCustomers = () => [
  { nama: 'Bu Lisferi', alamat: '-', telepon: '-', email: '', isdefault: true },
  { nama: 'Dapur Lezzati', alamat: '-', telepon: '-', email: '', isdefault: true },
  { nama: 'Maruf', alamat: '-', telepon: '-', email: '', isdefault: true }
]

// Initialize with sample data if empty
export const initializeSampleData = async () => {
  const products = await getProducts();
  if (products.length === 0) {
    const defaultProducts = getDefaultProducts();
    for (const product of defaultProducts) {
      await saveProduct(product);
    }
  }
  const customers = await getCustomers();
  if (customers.length === 0) {
    const defaultCustomers = getDefaultCustomers();
    for (const customer of defaultCustomers) {
      await saveCustomer(customer);
    }
  }
  setDataVersion(CURRENT_DATA_VERSION);
}

// Smart sync function to add new default data without removing existing data
export const syncDefaultData = async () => {
  const currentVersion = getDataVersion();
  if (currentVersion === CURRENT_DATA_VERSION) {
    return { updated: false, message: 'Data sudah up to date' };
  }
  let updatedProducts = 0;
  let updatedCustomers = 0;
  const existingProducts = await getProducts();
  const defaultProducts = getDefaultProducts();
  const existingProductNames = existingProducts.map(p => p.nama);
  for (const defaultProduct of defaultProducts) {
    if (!existingProductNames.includes(defaultProduct.nama)) {
      await saveProduct(defaultProduct);
      updatedProducts++;
    }
  }
  const existingCustomers = await getCustomers();
  const defaultCustomers = getDefaultCustomers();
  const existingCustomerNames = existingCustomers.map(c => c.nama);
  for (const defaultCustomer of defaultCustomers) {
    if (!existingCustomerNames.includes(defaultCustomer.nama)) {
      await saveCustomer(defaultCustomer);
      updatedCustomers++;
    }
  }
  setDataVersion(CURRENT_DATA_VERSION);
  const message = `Berhasil menambahkan ${updatedProducts} produk baru dan ${updatedCustomers} pelanggan baru`;
  return {
    updated: updatedProducts > 0 || updatedCustomers > 0,
    message,
    updatedProducts,
    updatedCustomers
  };
}

// Auto-sync function to be called on app startup
export const autoSyncDefaultData = async () => {
  try {
    const products = await getProducts();
    const customers = await getCustomers();
    if (products.length === 0 || customers.length === 0) {
      await initializeSampleData();
      return {
        isFirstTime: true,
        message: 'Aplikasi diinisialisasi dengan data default'
      };
    } else {
      return await syncDefaultData();
    }
  } catch (error) {
    console.error('Error in auto-sync:', error);
    return {
      updated: false,
      error: true,
      message: 'Terjadi kesalahan saat sinkronisasi data'
    };
  }
}

// Reset all data and reinitialize with default data
export const resetToDefaultData = () => {
  // Clear existing data
  localStorage.removeItem(STORAGE_KEYS.PRODUCTS)
  localStorage.removeItem(STORAGE_KEYS.CUSTOMERS)
  localStorage.removeItem(STORAGE_KEYS.INVOICES)
  localStorage.removeItem(STORAGE_KEYS.DATA_VERSION)

  // Reinitialize with default data
  initializeSampleData()
}


