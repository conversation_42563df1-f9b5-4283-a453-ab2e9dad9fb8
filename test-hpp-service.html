<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test HPP Service</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>HPP Service Test</h1>
    <p>This page tests the HPP service to ensure the error is fixed.</p>
    
    <button onclick="testErrorHandling()">Test Error Handling</button>
    <button onclick="testArrayAccess()">Test Array Access</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <div id="results"></div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function testErrorHandling() {
            addResult('<h3>Testing Error Handling</h3>', 'info');
            
            // Test cases that would previously cause errors
            const testCases = [
                { name: 'undefined ingredients', record: { id: '1', productName: 'Test', ingredients: undefined, hpp: 1000 } },
                { name: 'null ingredients', record: { id: '2', productName: 'Test', ingredients: null, hpp: 2000 } },
                { name: 'non-array ingredients', record: { id: '3', productName: 'Test', ingredients: 'not an array', hpp: 3000 } },
                { name: 'empty object', record: {} },
                { name: 'null record', record: null },
                { name: 'valid record', record: { id: '6', productName: 'Valid', ingredients: [{ ingredientId: 1, quantity: 100 }], hpp: 800 } }
            ];

            testCases.forEach((testCase, index) => {
                try {
                    const record = testCase.record;
                    
                    // Test the specific operation that was failing
                    const ingredients = Array.isArray(record?.ingredients) ? record.ingredients : [];
                    const hasIngredients = ingredients.length > 0;
                    const ingredientCount = ingredients.length;
                    const showMore = ingredientCount > 2;
                    
                    addResult(`✅ ${testCase.name}: ingredients.length = ${ingredientCount}, showMore = ${showMore}`, 'success');
                } catch (error) {
                    addResult(`❌ ${testCase.name}: ${error.message}`, 'error');
                }
            });
        }

        function testArrayAccess() {
            addResult('<h3>Testing Array Access Patterns</h3>', 'info');
            
            const testData = [
                undefined,
                null,
                'not an array',
                [],
                [1, 2],
                [1, 2, 3, 4, 5]
            ];

            testData.forEach((data, index) => {
                try {
                    // Simulate the original problematic code pattern
                    const safeArray = Array.isArray(data) ? data : [];
                    const sliced = safeArray.slice(0, 2);
                    const hasMore = safeArray.length > 2;
                    const moreCount = safeArray.length - 2;
                    
                    addResult(`✅ Test ${index + 1}: input=${JSON.stringify(data)}, length=${safeArray.length}, hasMore=${hasMore}, moreCount=${moreCount}`, 'success');
                } catch (error) {
                    addResult(`❌ Test ${index + 1}: ${error.message}`, 'error');
                }
            });
        }

        // Run initial test
        window.onload = function() {
            addResult('Page loaded successfully. Click buttons to run tests.', 'info');
        };
    </script>
</body>
</html>
