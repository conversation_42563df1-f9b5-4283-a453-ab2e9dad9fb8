import React, { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Plus, Trash2, Save } from 'lucide-react'
import { getProducts, getCustomers, updateInvoice, getInvoiceById } from '../services/storage'
import { useGlobalDialog } from '../contexts/DialogContext'
import ModernButton from '../components/ModernButton'

const EditInvoice = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const [products, setProducts] = useState([])
  const [customers, setCustomers] = useState([])
  const [loading, setLoading] = useState(true)
  const [formData, setFormData] = useState({
    pelangganid: '',
    createdat: new Date().toISOString().split('T')[0],
    items: [{ produkId: '', quantity: 1, harga: 0, createdat: new Date().toISOString().split('T')[0] }],
    status: 'unpaid'
  })
  const { alert, success, error: showError } = useGlobalDialog()

  useEffect(() => {
    loadData()
  }, [id])

  const loadData = async () => {
    try {
      // Load products and customers
      setProducts(await getProducts())
      setCustomers(await getCustomers())

      // Load existing invoice
      const invoice = await getInvoiceById(id)
      if (!invoice) {
        await showError({
          title: 'Invoice Tidak Ditemukan',
          message: 'Invoice yang Anda cari tidak ditemukan.'
        })
        navigate('/invoices')
        return
      }

      // Ensure items exist and is an array, then add date field for backward compatibility
      const items = Array.isArray(invoice.items) ? invoice.items : []
      const itemsWithDates = items.map(item => {
        const itemDate = item.createdat || invoice.createdat
        return {
          ...item,
          createdat: itemDate ? new Date(itemDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]
        }
      })

      setFormData({
        pelangganid: invoice.pelangganid,
        createdat: invoice.createdat ? new Date(invoice.createdat).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        items: itemsWithDates,
        status: invoice.status
      })
    } catch (error) {
      console.error('Error loading invoice:', error)
      await showError({
        title: 'Error',
        message: 'Terjadi kesalahan saat memuat data invoice.'
      })
      navigate('/invoices')
    } finally {
      setLoading(false)
    }
  }

  const handleCustomerChange = (customerId) => {
    setFormData({ ...formData, pelangganid: customerId })
  }

  const handleItemChange = (index, field, value) => {
    const newItems = [...formData.items]
    newItems[index][field] = value
    
    // Auto-fill price when product is selected
    if (field === 'produkId') {
      const product = products.find(p => p.id === value)
      if (product) {
        newItems[index].harga = product.harga
      }
    }
    
    setFormData({ ...formData, items: newItems })
  }

  const addItem = () => {
    const newItem = { 
      produkId: '', 
      quantity: 1, 
      harga: 0, 
      createdat: new Date().toISOString().split('T')[0] 
    }
    
    // Add new item and sort all items by date
    const newItems = [...formData.items, newItem]
    newItems.sort((a, b) => new Date(a.createdat) - new Date(b.createdat))
    
    setFormData({
      ...formData,
      items: newItems
    })
  }

  const removeItem = (index) => {
    if (formData.items.length > 1) {
      const newItems = formData.items.filter((_, i) => i !== index)
      setFormData({ ...formData, items: newItems })
    }
  }

  const calculateTotal = () => {
    return formData.items.reduce((total, item) => {
      return total + (item.quantity * item.harga)
    }, 0)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    // Validation
    if (!formData.pelangganid) {
      await alert({
        title: 'Validasi Error',
        message: 'Silakan pilih pelanggan'
      })
      return
    }

    if (formData.items.some(item => !item.produkId)) {
      await alert({
        title: 'Validasi Error',
        message: 'Silakan pilih produk untuk semua item'
      })
      return
    }

    try {
      // Sort items by date before saving
      const sortedItems = [...formData.items].sort((a, b) => new Date(a.createdat) - new Date(b.createdat))
      
      // Calculate total
      const total = calculateTotal()

      // Update invoice with sorted items
      const invoiceData = {
        ...formData,
        items: sortedItems,
        total
      }

      const updatedInvoice = updateInvoice(id, invoiceData)

      if (updatedInvoice) {
        await success({
          title: 'Berhasil!',
          message: 'Invoice berhasil diperbarui!'
        })
        navigate(`/invoices/${id}`)
      } else {
        await showError({
          title: 'Gagal!',
          message: 'Gagal memperbarui invoice. Silakan coba lagi.'
        })
      }
    } catch (error) {
      await showError({
        title: 'Error!',
        message: 'Terjadi kesalahan saat memperbarui invoice.'
      })
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount)
  }

  const getProductName = (productId) => {
    const product = products.find(p => p.id === productId)
    return product ? product.nama : ''
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Memuat data invoice...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-emerald-600 to-emerald-500 bg-clip-text text-transparent">
          Edit Invoice
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Invoice Info */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-gray-900">Informasi Invoice</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Pelanggan</label>
                <select
                  required
                  value={formData.pelangganid}
                  onChange={(e) => handleCustomerChange(e.target.value)}
                  className="input-field"
                >
                  <option value="">Pilih Pelanggan</option>
                  {Array.isArray(customers) && customers.map(customer => (
                    <option key={customer.id} value={customer.id}>
                      {customer.nama}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Tanggal</label>
                <input
                  type="date"
                  required
                  value={formData.createdat}
                  onChange={(e) => setFormData({ ...formData, createdat: e.target.value })}
                  className="input-field"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Items */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-gray-900">Item Invoice</h2>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              {Array.isArray(formData.items) && formData.items.map((item, index) => (
                <div key={index} className="bg-gray-50 rounded-xl p-4 border border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Produk</label>
                      <select
                        required
                        value={item.produkId}
                        onChange={(e) => handleItemChange(index, 'produkId', e.target.value)}
                        className="input-field"
                      >
                        <option value="">Pilih Produk</option>
                        {Array.isArray(products) && products.map(product => (
                          <option key={product.id} value={product.id}>
                            {product.nama} - {formatCurrency(product.harga)}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Tanggal Item</label>
                      <input
                        type="date"
                        required
                        value={item.createdat}
                        onChange={(e) => handleItemChange(index, 'createdat', e.target.value)}
                        className="input-field"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                      <input
                        type="number"
                        required
                        min="1"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value))}
                        className="input-field"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Harga</label>
                      <input
                        type="number"
                        required
                        min="0"
                        step="0.01"
                        value={item.harga}
                        onChange={(e) => handleItemChange(index, 'harga', parseFloat(e.target.value))}
                        className="input-field"
                      />
                    </div>
                  </div>
                  <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
                    <div className="text-sm text-gray-600">
                      Subtotal: <span className="font-semibold text-emerald-600">{formatCurrency(item.quantity * item.harga)}</span>
                    </div>
                    <ModernButton
                      type="button"
                      onClick={() => removeItem(index)}
                      color="error"
                      size="small"
                      disabled={formData.items.length === 1}
                      style={{ minWidth: 32, minHeight: 32, padding: 0, background: 'transparent', boxShadow: 'none' }}
                      startIcon={<Trash2 className="h-4 w-4" />}
                    />
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 flex justify-center">
              <ModernButton
                type="button"
                onClick={addItem}
                color="primary"
                size="medium"
                fullWidth
                style={{ fontWeight: 600, fontSize: 15, borderRadius: 10 }}
                startIcon={<Plus className="h-4 w-4 mr-2" />}
              >
                Tambah Item
              </ModernButton>
            </div>
          </div>

          {/* Total */}
          <div className="mt-6 pt-6 border-t border-gray-200 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg p-4">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600">
                <p>Total {formData.items.length} item(s)</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600 mb-1">Total Pembayaran</p>
                <p className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-emerald-500 bg-clip-text text-transparent">
                  {formatCurrency(calculateTotal())}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-col sm:flex-row justify-end gap-4">
          <ModernButton
            type="button"
            onClick={() => navigate(`/invoices/${id}`)}
            color="inherit"
            fullWidth
            style={{ minWidth: 90, fontSize: 15 }}
          >
            Batal
          </ModernButton>
          <ModernButton
            type="submit"
            color="primary"
            fullWidth
            style={{ minWidth: 90, fontSize: 15 }}
            startIcon={<Save className="h-4 w-4 mr-2" />}
          >
            Perbarui Invoice
          </ModernButton>
        </div>
      </form>
    </div>
  )
}

export default EditInvoice
