import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Plus, Trash2, Save } from 'lucide-react'
import { getProducts, getCustomers, saveInvoice } from '../services/storage'
import { useGlobalDialog } from '../contexts/DialogContext'
import ModernButton from '../components/ModernButton'
import Select from 'react-select'

const CreateInvoice = () => {
  const navigate = useNavigate()
  const [products, setProducts] = useState([])
  const [customers, setCustomers] = useState([])
  
  // Format products for react-select
  const productOptions = products.map(product => ({
    value: product.id,
    label: `${product.nama} - ${new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(product.harga)}`,
    data: product
  }))
  const [formData, setFormData] = useState({
    pelangganid: '',
    createdat: new Date().toISOString().split('T')[0],
    items: [{ produkId: '', quantity: 1, harga: 0, createdat: new Date().toISOString().split('T')[0] }],
    status: 'unpaid'
  })
  const { alert, success, error: showError } = useGlobalDialog()

  useEffect(() => {
    async function loadData() {
      const productsRaw = await getProducts();
      setProducts(Array.isArray(productsRaw) ? productsRaw : []);
      const customersRaw = await getCustomers();
      setCustomers(Array.isArray(customersRaw) ? customersRaw : []);
    }
    loadData();
  }, [])

  const handleCustomerChange = (customerId) => {
    setFormData({ ...formData, pelangganid: customerId })
  }

  const handleItemChange = (index, field, value) => {
    const newItems = [...formData.items]
    newItems[index][field] = value
    setFormData({ ...formData, items: newItems })
  }

  // Handle product selection from dropdown
  const handleProductSelect = (selectedOption, action, index) => {
    if (action.action === 'select-option' && selectedOption) {
      const newItems = [...formData.items]
      newItems[index].produkId = selectedOption.value
      newItems[index].harga = selectedOption.data.harga
      setFormData({ ...formData, items: newItems })
    } else if (action.action === 'clear') {
      const newItems = [...formData.items]
      newItems[index].produkId = ''
      newItems[index].harga = 0
      setFormData({ ...formData, items: newItems })
    }
  }

  const addItem = () => {
    const newItem = {
      produkId: '',
      quantity: 1,
      harga: 0,
      createdat: new Date().toISOString().split('T')[0]
    }

    setFormData({
      ...formData,
      items: [...formData.items, newItem]
    })
  }

  const removeItem = (index) => {
    if (formData.items.length > 1) {
      const newItems = formData.items.filter((_, i) => i !== index)
      setFormData({ ...formData, items: newItems })
    }
  }

  const calculateTotal = () => {
    return formData.items.reduce((total, item) => {
      return total + (item.quantity * item.harga)
    }, 0)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    // Validate form
    if (!formData.pelangganid) {
      await alert({
        title: 'Validasi Form',
        message: 'Pilih pelanggan terlebih dahulu'
      })
      return
    }

    if (formData.items.some(item => !item.produkId || item.quantity <= 0)) {
      await alert({
        title: 'Validasi Form',
        message: 'Pastikan semua item memiliki produk dan quantity yang valid'
      })
      return
    }

    try {
      // Sort items by date before saving
      const sortedItems = [...formData.items].sort((a, b) => new Date(a.createdat) - new Date(b.createdat))
      
      // Calculate total
      const total = calculateTotal()

      // Save invoice with sorted items
      const invoiceData = {
        ...formData,
        items: sortedItems,
        total
      }

      const savedInvoice = saveInvoice(invoiceData)

      if (savedInvoice) {
        await success({
          title: 'Berhasil!',
          message: 'Invoice berhasil dibuat!'
        })
        navigate('/invoices')
      } else {
        await showError({
          title: 'Gagal!',
          message: 'Gagal membuat invoice. Silakan coba lagi.'
        })
      }
    } catch (error) {
      await showError({
        title: 'Error!',
        message: 'Terjadi kesalahan saat membuat invoice.'
      })
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount)
  }

  const getProductName = (productId) => {
    const product = products.find(p => p.id === productId)
    return product ? product.nama : ''
  }

  return (
    <div className="max-w-5xl mx-auto space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-emerald-600 to-emerald-500 bg-clip-text text-transparent">
          Buat Invoice Baru
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Invoice Info */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-gray-900">Informasi Invoice</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Pelanggan</label>
                <select
                  required
                  value={formData.pelangganid}
                  onChange={(e) => handleCustomerChange(e.target.value)}
                  className="input-field"
                >
                  <option value="">Pilih Pelanggan</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.id}>
                      {customer.nama}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Tanggal</label>
                <input
                  type="date"
                  required
                  value={formData.createdat}
                  onChange={(e) => setFormData({ ...formData, createdat: e.target.value })}
                  className="input-field"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Items */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-gray-900">Item Invoice</h2>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              {formData.items.map((item, index) => (
                <div key={index} className="bg-gray-50 rounded-xl p-4 border border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Produk</label>
                      <div className="w-full">
                        <Select
                          className="w-full"
                          classNamePrefix="select"
                          placeholder="Pilih Produk..."
                          isClearable
                          isSearchable
                          noOptionsMessage={() => "Produk tidak ditemukan"}
                          value={productOptions.find(option => option.value === item.produkId) || null}
                          onChange={(selectedOption, action) => handleProductSelect(selectedOption, action, index)}
                          options={productOptions}
                          classNames={{
                            control: ({ isFocused }) => `!min-h-[42px] !border ${isFocused ? '!border-blue-500 !ring-2 !ring-blue-200' : '!border-gray-300 hover:!border-gray-400'}`,
                            menu: () => '!z-[100] !mt-1 !border !border-gray-200 !rounded-md !shadow-lg',
                            option: ({ isFocused, isSelected }) => 
                              `!px-4 !py-2 !text-sm ${isSelected ? '!bg-blue-500 !text-white' : isFocused ? '!bg-gray-100' : '!bg-white'}`,
                            input: () => '!m-0 !p-0',
                            valueContainer: () => '!p-2',
                            singleValue: () => '!text-gray-900',
                            placeholder: () => '!text-gray-400',
                            indicatorsContainer: () => '!p-0',
                            clearIndicator: () => '!p-2 !text-gray-400 hover:!text-red-500',
                            dropdownIndicator: () => '!p-2 !text-gray-400 hover:!text-gray-600',
                            indicatorSeparator: () => '!bg-gray-300',
                          }}
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Tanggal Item</label>
                      <input
                        type="date"
                        required
                        value={item.createdat}
                        onChange={(e) => handleItemChange(index, 'createdat', e.target.value)}
                        className="input-field"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                      <input
                        type="number"
                        required
                        min="1"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value))}
                        className="input-field"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Harga</label>
                      <input
                        type="number"
                        required
                        min="0"
                        step="0.01"
                        value={item.harga}
                        onChange={(e) => handleItemChange(index, 'harga', parseFloat(e.target.value))}
                        className="input-field"
                      />
                    </div>
                  </div>
                  <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
                    <div className="text-sm text-gray-600">
                      Subtotal: <span className="font-semibold text-emerald-600">{formatCurrency(item.quantity * item.harga)}</span>
                    </div>
                    <ModernButton
                      type="button"
                      onClick={() => removeItem(index)}
                      color="error"
                      size="small"
                      disabled={formData.items.length === 1}
                      style={{ minWidth: 32, minHeight: 32, padding: 0, background: 'transparent', boxShadow: 'none' }}
                      startIcon={<Trash2 className="h-4 w-4" />}
                    />
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 flex justify-center">
              <ModernButton
                type="button"
                onClick={addItem}
                color="primary"
                size="medium"
                fullWidth
                style={{ fontWeight: 600, fontSize: 15, borderRadius: 10 }}
                startIcon={<Plus className="h-4 w-4 mr-2" />}
              >
                Tambah Item
              </ModernButton>
            </div>
          </div>

          {/* Total */}
          <div className="mt-6 pt-6 border-t border-gray-200 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg p-4">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600">
                <p>Total {formData.items.length} item(s)</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600 mb-1">Total Pembayaran</p>
                <p className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-emerald-500 bg-clip-text text-transparent">
                  {formatCurrency(calculateTotal())}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-col sm:flex-row justify-end gap-4">
          <ModernButton
            type="button"
            onClick={() => navigate('/invoices')}
            color="inherit"
            fullWidth
            style={{ minWidth: 90, fontSize: 15 }}
          >
            Batal
          </ModernButton>
          <ModernButton
            type="submit"
            color="primary"
            fullWidth
            style={{ minWidth: 90, fontSize: 15 }}
            startIcon={<Save className="h-4 w-4 mr-2" />}
          >
            Simpan Invoice
          </ModernButton>
        </div>
      </form>
    </div>
  )
}

export default CreateInvoice
