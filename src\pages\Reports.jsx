import React, { useState, useEffect } from 'react'
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  Users,
  FileText,
  Download,
  Filter
} from 'lucide-react'
import {
  exportRevenueData
} from '../services/reportService'
import { getInvoices, getCustomers } from '../services/storage'
import { startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear, format, parseISO, isWithinInterval, subDays, subWeeks, subMonths, subYears } from 'date-fns'
import { id } from 'date-fns/locale'
import { generateInvoiceImage } from '../services/imageService'
import { formatCurrency } from '../utils/formatters'
import ModernButton from '../components/ModernButton'
import ExpenseSummaryCard from '../components/ExpenseSummaryCard'

const Reports = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('month')
  const [customDateRange, setCustomDateRange] = useState({
    start: '',
    end: ''
  })
  const [useCustomRange, setUseCustomRange] = useState(false)
  const [reportData, setReportData] = useState(null)
  const [trendData, setTrendData] = useState([])
  const [topCustomers, setTopCustomers] = useState([])
  const [summary, setSummary] = useState(null)
  // Expense states
  const [expenseData, setExpenseData] = useState(null)
  const [expenseTrend, setExpenseTrend] = useState([])
  const [expenseSummary, setExpenseSummary] = useState(null)
  const [loading, setLoading] = useState(true)

  // Raw data for client-side filtering
  const [allInvoices, setAllInvoices] = useState([])
  const [allCustomers, setAllCustomers] = useState([])
  const [allExpenses, setAllExpenses] = useState([])

  const periodOptions = [
    { value: 'day', label: 'Hari Ini' },
    { value: 'week', label: 'Minggu Ini' },
    { value: 'month', label: 'Bulan Ini' },
    { value: 'year', label: 'Tahun Ini' }
  ]

  // Client-side filtering functions
  const getDateRange = (period, customRange = null) => {
    if (customRange && customRange.start && customRange.end) {
      return {
        start: startOfDay(parseISO(customRange.start)),
        end: endOfDay(parseISO(customRange.end))
      }
    }

    const now = new Date()
    switch (period) {
      case 'day':
        return { start: startOfDay(now), end: endOfDay(now) }
      case 'week':
        return { start: startOfWeek(now, { locale: id }), end: endOfWeek(now, { locale: id }) }
      case 'month':
        return { start: startOfMonth(now), end: endOfMonth(now) }
      case 'year':
        return { start: startOfYear(now), end: endOfYear(now) }
      default:
        return { start: startOfMonth(now), end: endOfMonth(now) }
    }
  }

  const filterInvoicesByPeriod = (invoices, period, customRange = null) => {
    const { start, end } = getDateRange(period, customRange)
    return invoices.filter(invoice => {
      if (invoice.status !== 'paid') return false
      const dateToUse = invoice.paidat || invoice.createdat
      const invoiceDate = parseISO(dateToUse)
      return isWithinInterval(invoiceDate, { start, end })
    })
  }

  const filterExpensesByPeriod = (expenses, period, customRange = null) => {
    const { start, end } = getDateRange(period, customRange)
    return expenses.filter(expense => {
      if (!expense.date) return false
      try {
        const expenseDate = parseISO(expense.date)
        return isWithinInterval(expenseDate, { start, end })
      } catch (error) {
        return false
      }
    })
  }

  // Load all data once on component mount
  const loadAllData = async () => {
    setLoading(true)
    try {
      // Load all invoices and customers
      const invoicesRaw = await getInvoices()
      const customersRaw = await getCustomers()
      const invoices = Array.isArray(invoicesRaw) ? invoicesRaw : []
      const customers = Array.isArray(customersRaw) ? customersRaw : []

      // Load all expenses
      let expenses = []
      try {
        const localExpenses = JSON.parse(localStorage.getItem('expenses') || '[]')
        expenses = Array.isArray(localExpenses) ? localExpenses : []
      } catch (error) {
        console.warn('Error reading expenses from localStorage:', error)
      }

      setAllInvoices(invoices)
      setAllCustomers(customers)
      setAllExpenses(expenses)

    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  // Calculate client-side report data
  const calculateReportData = () => {
    const customRange = useCustomRange ? customDateRange : null

    // Filter invoices for current period
    const filteredInvoices = filterInvoicesByPeriod(allInvoices, selectedPeriod, customRange)

    // Calculate revenue data
    const totalRevenue = filteredInvoices.reduce((sum, invoice) => sum + invoice.total, 0)
    const averagePerInvoice = filteredInvoices.length > 0 ? totalRevenue / filteredInvoices.length : 0

    const invoicesWithCustomers = filteredInvoices.map(invoice => ({
      ...invoice,
      customerName: allCustomers.find(c => c.id === invoice.pelangganid)?.nama || 'Unknown'
    }))

    setReportData({
      totalRevenue,
      totalInvoices: filteredInvoices.length,
      averagePerInvoice,
      invoices: invoicesWithCustomers,
      period: selectedPeriod
    })

    // Calculate trend data
    const count = selectedPeriod === 'day' ? 7 : selectedPeriod === 'week' ? 8 : 12
    const trendData = []
    const now = new Date()

    for (let i = count - 1; i >= 0; i--) {
      let start, end, label

      switch (selectedPeriod) {
        case 'day':
          const day = subDays(now, i)
          start = startOfDay(day)
          end = endOfDay(day)
          label = format(day, 'dd MMM', { locale: id })
          break
        case 'week':
          const week = subWeeks(now, i)
          start = startOfWeek(week, { locale: id })
          end = endOfWeek(week, { locale: id })
          label = `Minggu ${format(start, 'dd MMM', { locale: id })}`
          break
        case 'month':
          const month = subMonths(now, i)
          start = startOfMonth(month)
          end = endOfMonth(month)
          label = format(month, 'MMM yyyy', { locale: id })
          break
        case 'year':
          const year = subYears(now, i)
          start = startOfYear(year)
          end = endOfYear(year)
          label = format(year, 'yyyy')
          break
        default:
          const defaultMonth = subMonths(now, i)
          start = startOfMonth(defaultMonth)
          end = endOfMonth(defaultMonth)
          label = format(defaultMonth, 'MMM yyyy', { locale: id })
      }

      const periodInvoices = allInvoices.filter(invoice => {
        if (invoice.status !== 'paid') return false
        const dateToUse = invoice.paidat || invoice.createdat
        const invoiceDate = parseISO(dateToUse)
        return isWithinInterval(invoiceDate, { start, end })
      })

      const revenue = periodInvoices.reduce((sum, invoice) => sum + invoice.total, 0)

      trendData.push({
        period: label,
        revenue,
        invoiceCount: periodInvoices.length,
        date: format(start, 'yyyy-MM-dd')
      })
    }

    setTrendData(trendData)

    // Calculate top customers
    const customerRevenue = {}

    filteredInvoices.forEach(invoice => {
      const customerId = invoice.pelangganid
      if (!customerRevenue[customerId]) {
        customerRevenue[customerId] = {
          customerId,
          customerName: allCustomers.find(c => c.id === customerId)?.nama || 'Unknown',
          totalRevenue: 0,
          invoiceCount: 0
        }
      }
      customerRevenue[customerId].totalRevenue += invoice.total
      customerRevenue[customerId].invoiceCount++
    })

    const sortedCustomers = Object.values(customerRevenue).sort((a, b) => b.totalRevenue - a.totalRevenue)
    setTopCustomers(sortedCustomers.slice(0, 5))

    // Calculate summary for all periods
    const paidInvoices = allInvoices.filter(invoice => invoice.status === 'paid')

    const todayRevenue = filterInvoicesByPeriod(paidInvoices, 'day').reduce((sum, inv) => sum + inv.total, 0)
    const weekRevenue = filterInvoicesByPeriod(paidInvoices, 'week').reduce((sum, inv) => sum + inv.total, 0)
    const monthRevenue = filterInvoicesByPeriod(paidInvoices, 'month').reduce((sum, inv) => sum + inv.total, 0)
    const yearRevenue = filterInvoicesByPeriod(paidInvoices, 'year').reduce((sum, inv) => sum + inv.total, 0)

    setSummary({
      today: todayRevenue,
      week: weekRevenue,
      month: monthRevenue,
      year: yearRevenue
    })

    // Calculate expense data
    const filteredExpenses = filterExpensesByPeriod(allExpenses, selectedPeriod, customRange)
    const totalExpense = filteredExpenses.reduce((sum, expense) => sum + (Number(expense.amount) || 0), 0)
    const averagePerRecord = filteredExpenses.length > 0 ? totalExpense / filteredExpenses.length : 0

    setExpenseData({
      totalExpense,
      totalRecords: filteredExpenses.length,
      averagePerRecord,
      records: filteredExpenses,
      period: selectedPeriod
    })
  }

  // Load all data once on mount
  useEffect(() => {
    loadAllData()
  }, [])

  // Recalculate when filters change (client-side, no loading)
  useEffect(() => {
    if (allInvoices.length > 0) {
      calculateReportData()
    }
  }, [selectedPeriod, useCustomRange, customDateRange, allInvoices, allCustomers, allExpenses])

  const handleExportData = async () => {
    const exportData = await exportRevenueData(selectedPeriod)
    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })

    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `laporan-pendapatan-${selectedPeriod}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const handleExportImage = async () => {
    try {
      // Sembunyikan tombol export saat capture
      const exportButtons = document.querySelectorAll('.export-buttons')
      exportButtons.forEach(btn => btn.style.display = 'none')
      await generateInvoiceImage({ nomorinvoice: 'Laporan' }, {}, 'report-content')
      exportButtons.forEach(btn => btn.style.display = '')
    } catch (e) {
      // fallback error toast sudah di imageService
    }
  }

  const COLORS = ['var(--theme-500)', 'var(--theme-400)', 'var(--theme-600)', 'var(--theme-300)', 'var(--theme-700)']

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2" style={{ borderColor: 'var(--theme-500)' }}></div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold mb-2 text-center" style={{
          letterSpacing: 0.5,
          background: 'var(--theme-gradient, linear-gradient(90deg, #4caf50, #22d3ee))',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          color: 'transparent'
        }}>
          Laporan Pendapatan & Pengeluaran
        </h1>
        <div className="export-buttons flex gap-2">
          <ModernButton onClick={handleExportData} icon={<Download size={16} />}>
            Export Data
          </ModernButton>
          <ModernButton onClick={handleExportImage} icon={<FileText size={16} />}>
            Export Gambar
          </ModernButton>
        </div>
      </div>

      {/* Period Selector */}
      <div className="flex flex-wrap gap-2 items-center">
        <label className="font-semibold"><Filter size={16} className="inline mr-1" />Periode:</label>
        {periodOptions.map(opt => (
          <button
            key={opt.value}
            className={`px-3 py-1 rounded-full font-medium border transition-colors ${selectedPeriod === opt.value ? 'bg-[var(--theme-500)] text-white' : 'bg-[var(--theme-50)] text-[var(--theme-700)] border-[var(--theme-200)]'}`}
            onClick={() => { setSelectedPeriod(opt.value); setUseCustomRange(false); }}
          >
            {opt.label}
          </button>
        ))}
        <label className="ml-4 flex items-center gap-1">
          <input type="checkbox" checked={useCustomRange} onChange={e => setUseCustomRange(e.target.checked)} className="accent-[var(--theme-500)]" />
          Custom Range
        </label>
        {useCustomRange && (
          <>
            <input type="date" value={customDateRange.start} onChange={e => setCustomDateRange(r => ({ ...r, start: e.target.value }))} className="ml-2 px-2 py-1 rounded border border-[var(--theme-200)]" />
            <span>-</span>
            <input type="date" value={customDateRange.end} onChange={e => setCustomDateRange(r => ({ ...r, end: e.target.value }))} className="px-2 py-1 rounded border border-[var(--theme-200)]" />
          </>
        )}
      </div>

      <div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {periodOptions.map(opt => {
            // Get revenue from summary data based on period
            let revenue = 0;
            if (summary) {
              switch (opt.value) {
                case 'day':
                  revenue = summary.today || 0;
                  break;
                case 'week':
                  revenue = summary.week || 0;
                  break;
                case 'month':
                  revenue = summary.month || 0;
                  break;
                case 'year':
                  revenue = summary.year || 0;
                  break;
                default:
                  revenue = 0;
              }
            }

            const isActive = selectedPeriod === opt.value && !useCustomRange;
            return (
              <div
                key={opt.value}
                className={`relative overflow-hidden rounded-xl bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:border-gray-300 transition-all duration-300 hover:shadow-lg ${isActive ? 'ring-2 ring-blue-500 shadow-lg border-blue-200' : ''}`}
                style={{ minHeight: 140 }}
              >
                <div className="p-6">
                  {/* Header with icon and label */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-lg bg-gradient-to-br from-green-100 to-emerald-100">
                        <DollarSign className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <h3 className="text-sm font-semibold text-gray-700 uppercase tracking-wide">{opt.label}</h3>
                        <p className="text-xs text-gray-500 mt-0.5">Total Pendapatan</p>
                      </div>
                    </div>
                  </div>

                  {/* Revenue amount */}
                  <div className="space-y-1">
                    {loading ? (
                      <div className="animate-pulse">
                        <div className="h-8 bg-gray-200 rounded w-32 mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded w-24"></div>
                      </div>
                    ) : (
                      <>
                        <p className="text-2xl font-bold text-gray-900 leading-tight">
                          {formatCurrency(revenue)}
                        </p>
                        <div className="flex items-center space-x-2">
                          <div className="h-1 w-8 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"></div>
                          <span className="text-xs text-gray-500">Hanya invoice lunas</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                {/* Active indicator */}
                {isActive && (
                  <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-blue-500"></div>
                )}
              </div>
            );
          })}
        </div>
        <div className="mt-4">
          <ExpenseSummaryCard
            selectedPeriod={selectedPeriod}
            customDateRange={customDateRange}
            useCustomRange={useCustomRange}
            periodOptions={periodOptions}
            className="col-span-1 md:col-span-2 lg:col-span-4"
          />
        </div>
        {/* Net Income Card */}
        <div className="mt-6">
          <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-emerald-50 to-green-100 border border-emerald-200 hover:border-emerald-300 transition-all duration-300 hover:shadow-lg">
            <div className="p-6">
              {/* Header with icon and label */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-emerald-100 to-green-200">
                    <TrendingUp className="h-5 w-5 text-emerald-600" />
                  </div>
                  <div>
                    <h3 className="text-sm font-semibold text-emerald-700 uppercase tracking-wide">Pendapatan Bersih</h3>
                    <p className="text-xs text-emerald-500 mt-0.5">
                      Periode: {useCustomRange && customDateRange?.start && customDateRange?.end
                        ? `${customDateRange.start} - ${customDateRange.end}`
                        : periodOptions.find(opt => opt.value === selectedPeriod)?.label || ''}
                    </p>
                  </div>
                </div>
              </div>

              {/* Net Income amount */}
              <div className="space-y-1">
                {loading ? (
                  <div className="animate-pulse">
                    <div className="h-8 bg-emerald-200 rounded w-40 mb-2"></div>
                    <div className="h-3 bg-emerald-200 rounded w-32"></div>
                  </div>
                ) : (
                  <>
                    {(() => {
                      // Calculate net income
                      let expenses = [];
                      try {
                        const stored = localStorage.getItem('expenses');
                        expenses = stored ? JSON.parse(stored) : [];
                      } catch { expenses = []; }

                      // Filter expenses by period (simplified logic)
                      function parseDate(dateStr) {
                        if (!dateStr) return null;
                        const [year, month, day] = dateStr.split('-').map(Number);
                        return new Date(year, month - 1, day);
                      }

                      function filterExpensesByPeriod(expenses, period, customDateRange, useCustomRange) {
                        if (!Array.isArray(expenses)) return [];
                        const now = new Date();
                        let start, end;
                        if (useCustomRange && customDateRange?.start && customDateRange?.end) {
                          start = parseDate(customDateRange.start);
                          end = parseDate(customDateRange.end);
                          end.setHours(23, 59, 59, 999);
                        } else {
                          switch (period) {
                            case 'day':
                              start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                              end = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
                              break;
                            case 'week': {
                              const dayOfWeek = now.getDay() || 7;
                              start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek + 1);
                              end = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
                              break;
                            }
                            case 'month':
                              start = new Date(now.getFullYear(), now.getMonth(), 1);
                              end = new Date(now.getFullYear(), now.getMonth() + 1, 1);
                              break;
                            case 'year':
                              start = new Date(now.getFullYear(), 0, 1);
                              end = new Date(now.getFullYear() + 1, 0, 1);
                              break;
                            default:
                              start = null;
                              end = null;
                          }
                        }
                        return expenses.filter(exp => {
                          if (!exp.date) return false;
                          const d = parseDate(exp.date);
                          if (start && d < start) return false;
                          if (end && d >= end) return false;
                          return true;
                        });
                      }

                      const filteredExpenses = filterExpensesByPeriod(expenses, selectedPeriod, customDateRange, useCustomRange);
                      const totalExpense = filteredExpenses.reduce((sum, exp) => sum + (exp.amount || 0), 0);
                      const totalRevenue = reportData?.totalRevenue || 0;
                      const netIncome = totalRevenue - totalExpense;
                      const isPositive = netIncome >= 0;

                      return (
                        <>
                          <p className={`text-2xl font-bold leading-tight ${isPositive ? 'text-emerald-800' : 'text-red-700'}`}>
                            {formatCurrency(netIncome)}
                          </p>
                          <div className="flex items-center space-x-2">
                            <div className={`h-1 w-8 rounded-full ${isPositive ? 'bg-gradient-to-r from-emerald-400 to-green-500' : 'bg-gradient-to-r from-red-400 to-red-500'}`}></div>
                            <span className={`text-xs ${isPositive ? 'text-emerald-600' : 'text-red-600'}`}>
                              {isPositive ? 'Keuntungan' : 'Kerugian'} periode ini
                            </span>
                          </div>
                        </>
                      );
                    })()}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Revenue Trend Chart */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-gray-900">Tren Pendapatan</h2>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis
                    dataKey="period"
                    stroke="#666"
                    fontSize={12}
                  />
                  <YAxis
                    stroke="#666"
                    fontSize={12}
                    tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
                  />
                  <Tooltip
                    formatter={(value) => [formatCurrency(value), 'Pendapatan']}
                    labelStyle={{ color: '#333' }}
                    contentStyle={{
                      backgroundColor: '#fff',
                      border: '1px solid #e5e7eb',
                      borderRadius: '8px'
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="revenue"
                    stroke="var(--theme-500)"
                    strokeWidth={3}
                    dot={{ fill: 'var(--theme-500)', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: 'var(--theme-500)', strokeWidth: 2 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Invoice Count Chart */}
          <div className="card">
            <div className="card-header">
              <h2 className="text-lg font-semibold text-gray-900">Jumlah Invoice</h2>
            </div>
            <div className="p-6">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis
                    dataKey="period"
                    stroke="#666"
                    fontSize={12}
                  />
                  <YAxis
                    stroke="#666"
                    fontSize={12}
                  />
                  <Tooltip
                    formatter={(value) => [value, 'Jumlah Invoice']}
                    labelStyle={{ color: '#333' }}
                    contentStyle={{
                      backgroundColor: '#fff',
                      border: '1px solid #e5e7eb',
                      borderRadius: '8px'
                    }}
                  />
                  <Bar
                    dataKey="invoiceCount"
                    fill="var(--theme-500)"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* Top Customers */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div className="card">
            <div className="card-header">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <Users className="h-5 w-5 mr-2" style={{ color: 'var(--theme-500)' }} />
                Top Pelanggan
              </h2>
            </div>
            <div className="p-6">
              {topCustomers.length > 0 ? (
                <div className="space-y-4">
                  {topCustomers.map((customer, index) => (
                    <div key={customer.customerId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm" style={{
                          background: index === 0 ? 'var(--theme-500)' :
                          index === 1 ? 'var(--theme-400)' :
                          index === 2 ? 'var(--theme-600)' : 'var(--theme-300)'
                        }}>
                          {index + 1}
                        </div>
                        <div className="ml-3">
                          <p className="font-medium text-gray-900">{customer.customerName}</p>
                          <p className="text-sm text-gray-500">{customer.invoiceCount} invoice</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold" style={{ color: 'var(--theme-600)' }}>{formatCurrency(customer.totalRevenue)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2 text-sm text-gray-500">Belum ada data pelanggan</p>
                </div>
              )}
            </div>
          </div>

          {/* Top Customers Pie Chart */}
          <div className="card">
            <div className="card-header">
              <h2 className="text-lg font-semibold text-gray-900">Distribusi Pendapatan</h2>
            </div>
            <div className="p-6">
              {topCustomers.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={topCustomers}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={(entry) => `${entry.customerName} ${((entry.totalRevenue / reportData?.totalRevenue) * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="totalRevenue"
                    >
                      {topCustomers.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(value)} />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <div className="text-center py-8">
                  <div className="mx-auto h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center">
                    <TrendingUp className="h-6 w-6 text-gray-400" />
                  </div>
                  <p className="mt-2 text-sm text-gray-500">Belum ada data untuk ditampilkan</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
  );
}

export default Reports;
