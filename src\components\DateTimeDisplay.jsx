import React, { useState, useEffect } from 'react'
import { Typography, Box } from '@mui/material'

const DateTimeDisplay = () => {
  const [currentDateTime, setCurrentDateTime] = useState(new Date())

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentDateTime(new Date())
    }, 1000) // Update every second

    return () => clearInterval(timer)
  }, [])

  const formatDateTime = (date) => {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ]

    const dayName = days[date.getDay()]
    const day = date.getDate()
    const month = months[date.getMonth()]
    const year = date.getFullYear()

    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return `${dayName}, ${day} ${month} ${year} | ${hours}.${minutes}`
  }

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Typography 
        variant="body2" 
        sx={{ 
          fontWeight: 500,
          color: 'var(--theme-700)',
          fontSize: '0.875rem',
          fontFamily: 'inherit'
        }}
      >
        {formatDateTime(currentDateTime)}
      </Typography>
    </Box>
  )
}

export default DateTimeDisplay
