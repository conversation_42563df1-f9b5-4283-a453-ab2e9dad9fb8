import { supabase } from './supabaseClient'
import { v4 as uuidv4 } from 'uuid'

class IngredientService {
  constructor() {
    this.storageKey = 'ingredients';
    this.ingredients = this.loadIngredients();
  }

  loadIngredients() {
    const savedIngredients = localStorage.getItem(this.storageKey);
    return savedIngredients ? JSON.parse(savedIngredients) : [];
  }

  saveIngredients() {
    localStorage.setItem(this.storageKey, JSON.stringify(this.ingredients));
  }

  async getAllIngredients() {
    const { data, error } = await supabase.from('bahan_baku').select('*')
    if (error) {
      console.error('Supabase getAllIngredients error:', error)
      return []
    }
    return data
  }

  async getIngredientById(id) {
    const { data, error } = await supabase.from('bahan_baku').select('*').eq('id', id).single()
    if (error) {
      console.error('Supabase getIngredientById error:', error)
      return null
    }
    return data
  }

  async addIngredient(ingredient) {
    const newIngredient = {
      id: uuidv4(),
      ...ingredient,
      createdat: new Date().toISOString()
    }
    const { data, error } = await supabase.from('bahan_baku').insert([newIngredient]).select()
    if (error) {
      console.error('Supabase addIngredient error:', error)
      return null
    }
    return data[0]
  }

  async updateIngredient(id, updates) {
    const { data, error } = await supabase.from('bahan_baku').update(updates).eq('id', id).select()
    if (error) {
      console.error('Supabase updateIngredient error:', error)
      return null
    }
    return data[0]
  }

  async deleteIngredient(id) {
    const { error } = await supabase.from('bahan_baku').delete().eq('id', id)
    if (error) {
      console.error('Supabase deleteIngredient error:', error)
      return false
    }
    return true
  }

  // Helper method to get ingredients with search filter
  searchIngredients(query = '') {
    const searchLower = query.toLowerCase();
    return this.ingredients.filter(ingredient => 
      ingredient.nama.toLowerCase().includes(searchLower) ||
      ingredient.satuan.toLowerCase().includes(searchLower)
    );
  }

  // Helper to calculate price per unit
  calculatePricePerUnit(ingredient) {
    if (!ingredient.berat || !ingredient.harga) return 0;
    return ingredient.harga / ingredient.berat;
  }
}

// Export a singleton instance
export const ingredientService = new IngredientService();
