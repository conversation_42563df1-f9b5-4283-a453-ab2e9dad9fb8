import React, { useState, useEffect, useCallback } from "react";
import {
  LayoutDashboard,
  Calculator,
  DollarSign,
  Package,
  PieChart,
  Plus,
  Search,
  Edit2,
  Trash2,
  X,
} from "lucide-react";
import { formatRupiah } from '../utils/formatting';
import { ingredientService } from "../services/ingredientService";
import { hppService } from "../services/hppService";
import { getProducts } from "../services/storage";
import HPPModal from "../components/HPPModal";
import { useGlobalDialog } from '../contexts/DialogContext';
import ModernButton from '../components/ModernButton';
import { ensureArray, sanitizeHPPRecord, validateHPPRecord } from '../utils/errorHandling';

const HPP = () => {
  const [formData, setFormData] = useState({
    productName: "",
    ingredients: [{ ingredientId: null, quantity: '', unit: 'g' }],
    laborCost: 0,
    utilitiesCost: 0,
    packagingCost: 0,
    otherCosts: 0,
    hpp: 0,
  });
  const [ingredients, setIngredients] = useState([]);
  const [products, setProducts] = useState([]);
  const [hppRecords, setHPPRecords] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [editingRecord, setEditingRecord] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);

  const loadHPPRecords = useCallback(async () => {
    setLoading(true)
    try {
      const allRecordsRaw = await hppService.getAllRecords();
      const sanitizedRecords = ensureArray(allRecordsRaw)
        .filter(validateHPPRecord)
        .map(sanitizeHPPRecord);
      setHPPRecords(sanitizedRecords);
    } catch (error) {
      console.error('Error loading HPP records:', error);
      setHPPRecords([]);
    } finally {
      setLoading(false)
    }
  }, []);

  useEffect(() => {
    const loadIngredients = async () => {
      const allIngredientsRaw = await ingredientService.getAllIngredients();
      setIngredients(Array.isArray(allIngredientsRaw) ? allIngredientsRaw : []);
    };

    const loadProducts = async () => {
      const allProductsRaw = await getProducts();
      setProducts(Array.isArray(allProductsRaw) ? allProductsRaw : []);
    };

    loadIngredients();
    loadProducts();
    loadHPPRecords();
  }, [loadHPPRecords]);



  const handleAddHPP = () => {
    setFormData({
      productName: "",
      ingredients: [{ ingredientId: null, quantity: '', unit: 'g' }],
      laborCost: 0,
      utilitiesCost: 0,
      packagingCost: 0,
      otherCosts: 0,
      hpp: 0,
    });
    setEditingRecord(null);
    setShowModal(true);
  };

  const handleEditHPP = (record) => {
    setFormData({
      ...record,
      ingredients: ensureArray(record.ingredients).length > 0
        ? ensureArray(record.ingredients).map(ingredient => ({
            ...ingredient,
            ingredientId: ingredient.ingredientId,
            quantity: ingredient.quantity,
            unit: ingredient.unit
          }))
        : [{ ingredientId: null, quantity: '', unit: 'g' }]
    });
    setEditingRecord(record);
    setShowModal(true);
  };

  // Helper function to calculate harga for a single ingredient
  const calculateIngredientHarga = (ingredient, quantity) => {
    const berat = parseFloat(ingredient.berat);
    const hargaTotal = parseFloat(ingredient.harga);
    
    // For kg, convert to grams
    const beratGram = ingredient.satuan === 'kg' ? berat * 1000 : berat;
    const jumlahGram = quantity * (ingredient.satuan === 'kg' ? 1000 : 1);
    
    return (jumlahGram / beratGram) * hargaTotal;
  };

  const calculateHPP = () => {
    // Calculate total cost based on selected ingredients
    const totalIngredientsCost = formData.ingredients.reduce((sum, ingredient) => {
      if (!ingredient.ingredientId) return sum;
      
      const selectedIngredient = ingredients.find(i => i.id === ingredient.ingredientId);
      if (!selectedIngredient) return sum;

      const quantity = parseFloat(ingredient.quantity) || 0;
      const ingredientHarga = calculateIngredientHarga(selectedIngredient, quantity);
      
      return sum + ingredientHarga;
    }, 0);

    const totalCost = (
      totalIngredientsCost +
      formData.laborCost +
      formData.utilitiesCost +
      formData.packagingCost +
      formData.otherCosts
    );

    setFormData(prev => ({ ...prev, hpp: totalCost }));
    return totalCost;
  };

  // Calculate HPP whenever form data changes
  useEffect(() => {
    calculateHPP();
  }, [formData.ingredients, formData.laborCost, formData.utilitiesCost, formData.packagingCost, formData.otherCosts, ingredients]);

  const { deleteConfirm, success, error: showError } = useGlobalDialog();

  const handleDeleteHPP = useCallback(async (id) => {
    const confirmed = await deleteConfirm({
      title: 'Hapus HPP',
      message: `Apakah Anda yakin ingin menghapus HPP ini?\n\nTindakan ini tidak dapat dibatalkan.`,
      confirmText: 'Hapus',
      cancelText: 'Batal'
    });

    if (confirmed) {
      try {
        await hppService.deleteRecord(id);
        await success({
          title: 'Berhasil!',
          message: 'HPP berhasil dihapus!'
        });
        loadHPPRecords();
      } catch (error) {
        await showError({
          title: 'Error!',
          message: 'Terjadi kesalahan saat menghapus HPP.'
        });
      }
    }
  }, [loadHPPRecords]);



  const handleSearch = async (e) => {
    setSearchQuery(e.target.value);
    const query = e.target.value;

    try {
      const filteredRecords = await hppService.searchRecords(query);
      const sanitizedRecords = ensureArray(filteredRecords)
        .filter(validateHPPRecord)
        .map(sanitizeHPPRecord);
      setHPPRecords(sanitizedRecords);
    } catch (error) {
      console.error('Error searching records:', error);
      setHPPRecords([]);
    }
  };

  const saveHPP = async () => {
    try {
      if (editingRecord) {
        await hppService.updateRecord(editingRecord.id, formData);
        setEditingRecord(null);
      } else {
        await hppService.addRecord(formData);
      }

      // Reload records after save
      await loadHPPRecords();

      setFormData({
        productName: "",
        ingredients: [{ ingredientId: null, quantity: '', unit: 'g' }],
        laborCost: 0,
        utilitiesCost: 0,
        packagingCost: 0,
        otherCosts: 0,
        hpp: 0,
      });
    } catch (error) {
      console.error('Error saving HPP:', error);
    }
  };

  return (
    <div className="max-w-[100vw] overflow-hidden">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1
  className="text-2xl font-bold"
  style={{ color: 'var(--theme-900, #1e293b)' }}
>
  Harga Pokok Produksi (HPP)
</h1>
<ModernButton
  onClick={handleAddHPP}
  color="primary"
  size="large"
  startIcon={<Plus style={{ width: 20, height: 20 }} />}
  style={{ borderRadius: 14, fontWeight: 700, fontSize: 16, padding: '12px 28px', minHeight: 48, minWidth: 140, letterSpacing: 0.5 }}
>
  Tambah HPP
</ModernButton>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <input
              type="text"
              placeholder="Cari HPP..."
              value={searchQuery}
              onChange={handleSearch}
              className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Search className="absolute right-3 top-3 w-5 h-5 text-gray-400" />
          </div>
        </div>

        {loading ? (
  <div className="flex justify-center items-center py-12">
    <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-emerald-500"></div>
  </div>
) : (
  <>
    {/* Desktop Table */}
    <div className="hidden md:block">
      <div className="table-responsive">
        <table className="table table-striped table-hover table-sm">
          <thead className="table-light">
            <tr>
              <th scope="col" className="text-start">Nama Produk</th>
              <th scope="col" className="text-start">HPP per Unit</th>
              <th scope="col" className="text-start">Bahan Baku</th>
              <th scope="col" className="text-start">Aksi</th>
            </tr>
          </thead>
          <tbody>
            {ensureArray(hppRecords).map((record) => (
              <tr key={record.id}>
                <td className="fw-medium py-2 align-middle text-nowrap small">{record.productName || 'N/A'}</td>
                <td className="fw-semibold text-success py-2 align-middle text-nowrap small">Rp {formatRupiah(record.hpp || 0)}</td>
                <td className="py-2 align-middle small">
                  <div className="d-flex flex-wrap gap-1">
                    {ensureArray(record.ingredients).map((ingredient, index) => {
                      const selectedIngredient = ingredients.find(i => i.id === ingredient.ingredientId);
                      if (!selectedIngredient) return null;
                      return (
                        <span key={index} className="badge bg-light text-dark border text-nowrap">
                          {selectedIngredient.nama} ({ingredient.quantity} {ingredient.unit})
                        </span>
                      );
                    })}
                  </div>
                </td>
                <td className="py-2 align-middle">
                  <div className="d-flex gap-1">
                    <button
                      onClick={() => handleEditHPP(record)}
                      className="btn btn-outline-success btn-sm d-flex align-items-center justify-content-center"
                      style={{ width: '28px', height: '28px' }}
                    >
                      <Edit2 className="h-3 w-3" />
                    </button>
                    <button
                      onClick={() => handleDeleteHPP(record.id)}
                      className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center"
                      style={{ width: '28px', height: '28px' }}
                    >
                      <Trash2 className="h-3 w-3" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
    {/* Mobile Table */}
    <div className="d-md-none">
      <div className="table-responsive">
        <table className="table table-sm">
          <thead className="table-light">
            <tr>
              <th scope="col" className="text-start small">Produk</th>
              <th scope="col" className="text-start small">HPP</th>
              <th scope="col" className="text-start small">Bahan</th>
              <th scope="col" className="text-start small">Aksi</th>
            </tr>
          </thead>
          <tbody>
            {ensureArray(hppRecords).map((record) => (
              <tr key={record.id}>
                <td className="py-2 align-middle">
                  <span className="fw-semibold small text-nowrap">{record.productName || 'N/A'}</span>
                </td>
                <td className="py-2 align-middle">
                  <span className="fw-semibold text-success small text-nowrap">Rp {formatRupiah(record.hpp || 0)}</span>
                </td>
                <td className="py-2 align-middle small">
                  <div className="d-flex flex-wrap gap-1">
                    {ensureArray(record.ingredients).slice(0, 2).map((ingredient, index) => {
                      const selectedIngredient = ingredients.find(i => i.id === ingredient.ingredientId);
                      if (!selectedIngredient) return null;
                      return (
                        <span key={index} className="badge bg-light text-dark border text-nowrap" style={{ fontSize: '0.7rem' }}>
                          {selectedIngredient.nama}
                        </span>
                      );
                    })}
                    {ensureArray(record.ingredients).length > 2 && (
                      <span className="badge bg-secondary text-nowrap" style={{ fontSize: '0.7rem' }}>
                        +{ensureArray(record.ingredients).length - 2}
                      </span>
                    )}
                  </div>
                </td>
                <td className="py-2 align-middle">
                  <div className="d-flex gap-1">
                    <button
                      onClick={() => handleEditHPP(record)}
                      className="btn btn-outline-success btn-sm d-flex align-items-center justify-content-center"
                      style={{ width: '28px', height: '28px' }}
                    >
                      <Edit2 className="h-3 w-3" />
                    </button>
                    <button
                      onClick={() => handleDeleteHPP(record.id)}
                      className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center"
                      style={{ width: '28px', height: '28px' }}
                    >
                      <Trash2 className="h-3 w-3" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  </>
)}

        <HPPModal
          isOpen={showModal}
          onClose={() => {
            setShowModal(false);
            setEditingRecord(null);
            setFormData({
              productName: "",
              totalProduction: 0,
              ingredients: [{ ingredientId: null, quantity: '', unit: 'g' }],
              laborCost: 0,
              utilitiesCost: 0,
              packagingCost: 0,
              otherCosts: 0,
              hpp: 0,
            });
          }}
          formData={formData}
          setFormData={setFormData}
          ingredients={ingredients}
          products={products}
          saveHPP={saveHPP}
          editingRecord={editingRecord}
        />
      </div>
    </div>
  );
};

export default HPP;
